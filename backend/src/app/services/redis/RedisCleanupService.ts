import { redis, safeRedisOperation, isRedisHealthy } from '../../config/redis';

/**
 * Service to clean up excessive Redis keys that are consuming storage
 * This service removes performance metrics, monitoring data, and other non-essential keys
 * Enhanced with graceful degradation for production stability
 */
export class RedisCleanupService {
  private static instance: RedisCleanupService;
  private isRedisAvailable: boolean = false;

  public static getInstance(): RedisCleanupService {
    if (!RedisCleanupService.instance) {
      RedisCleanupService.instance = new RedisCleanupService();
    }
    return RedisCleanupService.instance;
  }

  /**
   * Check if Redis is available before performing operations
   */
  private async checkRedisAvailability(): Promise<boolean> {
    try {
      this.isRedisAvailable = await isRedisHealthy();
      return this.isRedisAvailable;
    } catch (error) {
      console.warn('⚠️ Redis availability check failed:', error);
      this.isRedisAvailable = false;
      return false;
    }
  }

  /**
   * Clean up all performance metrics and monitoring keys
   * Enhanced with graceful degradation
   */
  async cleanupPerformanceMetrics(): Promise<void> {
    console.log('🧹 Starting Redis cleanup of performance metrics...');

    // Check if Redis is available first
    const isAvailable = await this.checkRedisAvailability();
    if (!isAvailable) {
      console.warn('⚠️ Redis is not available, skipping cleanup');
      return;
    }

    try {
      const patterns = [
        'metrics:performance:*',
        'cache:api:cache:stats:*',
        'cache:api:cache:response:*',
        'monitoring:*',
        'performance:*',
        'stats:*',
        'alerts:*',
        'audit:*',
        'usage:*',
        'health:*',
        'optimization:*',
        'dashboard:*',
        'analytics:*'
      ];

      let totalDeleted = 0;

      for (const pattern of patterns) {
        try {
          const keys = await safeRedisOperation(
            () => redis.keys(pattern),
            [],
            `Getting keys for pattern ${pattern}`
          );

          if (keys.length > 0) {
            console.log(`🗑️ Found ${keys.length} keys matching pattern: ${pattern}`);

            // Delete in batches to avoid overwhelming Redis
            const batchSize = 100;
            for (let i = 0; i < keys.length; i += batchSize) {
              const batch = keys.slice(i, i + batchSize);
              const deleted = await safeRedisOperation(
                () => redis.del(...batch),
                0,
                `Deleting batch of ${batch.length} keys`
              );
              totalDeleted += deleted;
              console.log(`   Deleted batch of ${deleted} keys`);

              // Small delay between batches
              await new Promise(resolve => setTimeout(resolve, 100));
            }
          }
        } catch (error) {
          console.error(`❌ Error cleaning pattern ${pattern}:`, error);
          // Continue with next pattern instead of failing completely
        }
      }

      console.log(`✅ Redis cleanup completed. Total keys deleted: ${totalDeleted}`);

      // Get current key count (safe operation)
      const info = await safeRedisOperation(
        () => redis.info('keyspace'),
        'Redis info unavailable',
        'Getting keyspace info'
      );
      console.log('📊 Current Redis keyspace info:', info);

    } catch (error) {
      console.error('❌ Error during Redis cleanup:', error);
      // Don't throw the error - just log it for graceful degradation
    }
  }

  /**
   * Clean up specific cache keys that might be consuming too much space
   * Enhanced with graceful degradation
   */
  async cleanupLargeCacheKeys(): Promise<void> {
    console.log('🧹 Cleaning up large cache keys...');

    // Check if Redis is available first
    const isAvailable = await this.checkRedisAvailability();
    if (!isAvailable) {
      console.warn('⚠️ Redis is not available, skipping large cache cleanup');
      return;
    }

    try {
      // Get all cache keys and check their size
      const cacheKeys = await safeRedisOperation(
        () => redis.keys('cache:*'),
        [],
        'Getting cache keys'
      );

      let totalFreed = 0;

      for (const key of cacheKeys) {
        try {
          const size = await safeRedisOperation(
            () => redis.memory('USAGE', key),
            0,
            `Getting memory usage for key ${key}`
          );

          // If key is larger than 1MB, consider removing it
          if (size && size > 1024 * 1024) {
            await safeRedisOperation(
              () => redis.del(key),
              0,
              `Deleting large cache key ${key}`
            );
            totalFreed += size;
            console.log(`🗑️ Removed large cache key: ${key} (${(size / 1024 / 1024).toFixed(2)}MB)`);
          }
        } catch (error) {
          // Ignore individual key errors and continue
          console.warn(`⚠️ Error processing key ${key}:`, error);
        }
      }

      if (totalFreed > 0) {
        console.log(`✅ Freed ${(totalFreed / 1024 / 1024).toFixed(2)}MB from large cache keys`);
      } else {
        console.log('✅ No large cache keys found to clean up');
      }

    } catch (error) {
      console.error('❌ Error cleaning large cache keys:', error);
      // Don't throw the error - just log it for graceful degradation
    }
  }

  /**
   * Get Redis memory usage statistics
   * Enhanced with graceful degradation
   */
  async getMemoryStats(): Promise<void> {
    // Check if Redis is available first
    const isAvailable = await this.checkRedisAvailability();
    if (!isAvailable) {
      console.warn('⚠️ Redis is not available, skipping memory stats');
      return;
    }

    try {
      const info = await safeRedisOperation(
        () => redis.info('memory'),
        'Redis memory info unavailable',
        'Getting Redis memory info'
      );
      console.log('📊 Redis Memory Statistics:');
      console.log(info);

      // Get key count by pattern
      const patterns = ['cache:*', 'auth:*', 'otp:*', 'sessions:*', 'metrics:*'];
      for (const pattern of patterns) {
        try {
          const keys = await safeRedisOperation(
            () => redis.keys(pattern),
            [],
            `Getting keys for pattern ${pattern}`
          );
          console.log(`   ${pattern}: ${keys.length} keys`);
        } catch (error) {
          console.log(`   ${pattern}: Error counting keys`);
        }
      }

    } catch (error) {
      console.error('❌ Error getting memory stats:', error);
      // Don't throw the error - just log it for graceful degradation
    }
  }

  /**
   * Emergency cleanup - removes all non-essential keys
   * Enhanced with graceful degradation
   */
  async emergencyCleanup(): Promise<void> {
    console.log('🚨 Starting emergency Redis cleanup...');

    // Check if Redis is available first
    const isAvailable = await this.checkRedisAvailability();
    if (!isAvailable) {
      console.warn('⚠️ Redis is not available, skipping emergency cleanup');
      return;
    }

    try {
      // Keep only essential keys
      const essentialPatterns = [
        'auth:*',
        'otp:*',
        'sessions:*',
        'user:*',
        'jwt:*'
      ];

      // Get all keys
      const allKeys = await safeRedisOperation(
        () => redis.keys('*'),
        [],
        'Getting all Redis keys'
      );

      const keysToDelete: string[] = [];

      for (const key of allKeys) {
        let isEssential = false;

        for (const pattern of essentialPatterns) {
          const regex = new RegExp('^' + pattern.replace('*', '.*') + '$');
          if (regex.test(key)) {
            isEssential = true;
            break;
          }
        }

        if (!isEssential) {
          keysToDelete.push(key);
        }
      }

      console.log(`🗑️ Emergency cleanup will delete ${keysToDelete.length} non-essential keys`);
      console.log(`✅ Keeping ${allKeys.length - keysToDelete.length} essential keys`);

      if (keysToDelete.length > 0) {
        // Delete in batches
        const batchSize = 100;
        let totalDeleted = 0;

        for (let i = 0; i < keysToDelete.length; i += batchSize) {
          const batch = keysToDelete.slice(i, i + batchSize);
          const deleted = await safeRedisOperation(
            () => redis.del(...batch),
            0,
            `Deleting emergency cleanup batch of ${batch.length} keys`
          );
          totalDeleted += deleted;
          console.log(`   Deleted batch: ${deleted} keys`);

          // Small delay between batches
          await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.log(`✅ Emergency cleanup completed. Deleted ${totalDeleted} keys`);
      }

    } catch (error) {
      console.error('❌ Error during emergency cleanup:', error);
      // Don't throw the error - just log it for graceful degradation
    }
  }
}

export const redisCleanupService = RedisCleanupService.getInstance();
